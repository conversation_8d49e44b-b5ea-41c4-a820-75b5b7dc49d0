import 'dart:io';
import 'dart:typed_data';

import 'package:flower_timemachine/common/file_utils.dart';
import 'package:flower_timemachine/common/resources.dart';
import 'package:flower_timemachine/controller/nurture_types_controller.dart';
import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/repository/monthly_nurture_cycle_repo.dart';
import 'package:flower_timemachine/models/nurture_types.dart';
import 'package:flower_timemachine/models/tag_info.dart';
import 'package:flower_timemachine/types/flower_monthly_cycles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:collection/collection.dart';

part 'add_flower_controller.g.dart';

@riverpod
AddFlowerController getAddFlowerController(GetAddFlowerControllerRef ref, Flower? flower) {
  return AddFlowerController.create(flower);
}

@riverpod
FutureOr<Map<int, FlowerNurtureCycle>> loadFlowerNurtureTypeCycle(
    LoadFlowerNurtureTypeCycleRef ref, AddFlowerController controller) async {
  return await controller.loadFlowerNurtureTypeCycle();
}

class AddFlowerController {
  final Flower? flower;

  Map<int, FlowerNurtureCycle> nurtureTypeCycle = {};
  Uint8List? _newAvatarData;
  String? _newName;
  bool _isChange = false;
  bool _isLoadNurtureTypeCycle = false;
  List<TagInfo>? _newSelectedTags;
  DateTime? _newArrivalTime;

  AddFlowerController._(this.flower);

  static AddFlowerController create(Flower? flower) {
    if (flower == null) {
      return AddFlowerController._(flower);
    } else {
      return AddFlowerController._(flower);
    }
  }

  Future<Map<int, FlowerNurtureCycle>> loadFlowerNurtureTypeCycle() async {
    if (_isLoadNurtureTypeCycle) {
      return nurtureTypeCycle;
    }

    // 新建花时使用默认配置
    if (flower == null) {
      final Map<int, FlowerNurtureCycle> cycles = {};
      for (final type in nurtureTypes) {
        cycles[type.id] = FlowerNurtureCycle(type, type.defaultCycle);
      }

      nurtureTypeCycle = cycles;
      _isLoadNurtureTypeCycle = true;
      return cycles;
    }

    // 编辑花时使用已经修改过的配置
    nurtureTypeCycle = await FlowerNurtureCycleMapping.getFlowerCycleMap(flower!.id);
    _isLoadNurtureTypeCycle = true;
    return nurtureTypeCycle;
  }

  int? getNurtureTypeCycle(NurtureType type) {
    return nurtureTypeCycle[type.id]?.cycle;
  }

  void setNurtureTypeCycle(NurtureType type, int cycle) {
    _isChange = true;
    nurtureTypeCycle[type.id] = FlowerNurtureCycle(type, cycle);
  }

  void setNewAvatar(Uint8List image) {
    _isChange = true;
    _newAvatarData = image;
  }

  Future<Flower> save() async {
    String? avatarFile;
    if (_newAvatarData != null) {
      final littleData = await FlutterImageCompress.compressWithList(_newAvatarData!, minHeight: 256, minWidth: 256);
      avatarFile = await writeToAvatarRecordDir(littleData);
    }

    if (flower == null) {
      return await _createFlower(avatarFile);
    } else {
      await _updateFlower(avatarFile);
      return flower!;
    }
  }

  Future<Flower> _createFlower(String? avatarFile) async {
    int? arrivalTime;
    if (_newArrivalTime != null) {
      arrivalTime = (_newArrivalTime!.millisecondsSinceEpoch / 1000).truncate();
    }

    return Flower.create(
        name: _newName!,
        avatar: avatarFile,
        tags: _newSelectedTags,
        nurtureCycle: nurtureTypeCycle.values.toList(),
        arrivalTime: arrivalTime);
  }

  Future<void> _updateFlower(String? avatarFile) async {
    int? arrivalTime;
    if (_newArrivalTime != null) {
      arrivalTime = (_newArrivalTime!.millisecondsSinceEpoch / 1000).truncate();
    }

    flower!.update(
        name: _newName,
        avatar: avatarFile,
        tags: _newSelectedTags,
        nurtureCycle: nurtureTypeCycle.values.toList(),
        arrivalTime: arrivalTime);
  }

  bool isChange() => _isChange;

  Image get avatar {
    final avatar = flower?.avatar;
    if (avatar != null) {
      return Image.file(File(avatar));
    } else {
      return Image.asset(R.iconFlower);
    }
  }

  List<TagInfo> get selectedTags => List.unmodifiable(flower?.tags ?? []);

  set selectedTags(List<TagInfo> newTags) {
    _newSelectedTags = [];
    _newSelectedTags!.addAll(newTags);

    final currentTags = flower?.tags ?? [];
    if (const ListEquality().equals(currentTags, newTags)) {
      return;
    }
    _isChange = true;
  }

  String? get name {
    return _newName ?? flower?.name;
  }

  set name(String? v) {
    if (flower == null && (v == null || v == "")) {
      return;
    }
    if (v == flower?.name) {
      return;
    }

    _isChange = true;
    _newName = v;
  }

  bool get loadNurtureCycle => _isLoadNurtureTypeCycle;

  List<NurtureType> get nurtureTypes => NurtureTypesController.get().enableTypes;

  DateTime get arrivalTime {
    if (_newArrivalTime != null) {
      return _newArrivalTime!;
    }

    if (flower?.arrivalTime != null) {
      return DateTime.fromMillisecondsSinceEpoch(flower!.arrivalTime! * 1000);
    }
    if (flower?.createTime != null) {
      return DateTime.fromMillisecondsSinceEpoch(flower!.createTime * 1000);
    }

    return DateTime.now();
  }

  set arrivalTime(DateTime time) {
    if (_newArrivalTime == time) {
      return;
    }

    _newArrivalTime = time;
    _isChange = true;
  }
}
